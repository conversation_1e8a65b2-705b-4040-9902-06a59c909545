import PyPDF2
import re
from typing import List

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai texto bruto de um arquivo PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                text += page_text + "\n"
        return text
    except Exception as e:
        print(f"Erro ao extrair texto do PDF: {e}")
        return ""

def clean_extracted_text(text: str) -> str:
    """Limpa e normaliza o texto extraído do PDF"""
    # Remover caracteres de controle e normalizar espaços
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

    # Corrigir quebras de linha desnecessárias (uma palavra por linha)
    # Múltiplas passadas para capturar todos os casos
    for _ in range(3):  # Fazer várias passadas
        # Padrão: palavra + \n + espaços + palavra
        text = re.sub(r'(\w+)\s*\n\s*(\w)', r'\1 \2', text)
        # Padrão: palavra + pontuação + \n + palavra
        text = re.sub(r'(\w+[,.;:])\s*\n\s*(\w)', r'\1 \2', text)
        # Padrão: palavra + \n + pontuação + \n + palavra
        text = re.sub(r'(\w+)\s*\n\s*([,.;:])\s*\n\s*(\w)', r'\1\2 \3', text)

    # Remover múltiplos espaços em branco
    text = re.sub(r' +', ' ', text)

    # Processar linha por linha para juntar linhas curtas
    lines = text.split('\n')
    processed_lines = []
    i = 0

    while i < len(lines):
        current_line = lines[i].strip()

        # Se a linha atual é muito curta (provavelmente palavra isolada)
        if current_line and len(current_line.split()) <= 2 and len(current_line) < 20:
            # Tentar juntar com a próxima linha
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line:
                    # Juntar as linhas
                    combined = current_line + ' ' + next_line
                    processed_lines.append(combined)
                    i += 2  # Pular a próxima linha
                    continue

        processed_lines.append(current_line)
        i += 1

    # Reconstruir o texto
    text = '\n'.join(processed_lines)

    # Remover múltiplas quebras de linha, mantendo parágrafos
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

    # Remover linhas vazias
    lines = [line for line in text.split('\n') if line.strip()]
    text = '\n'.join(lines)

    # Normalizar espaçamento entre parágrafos
    text = re.sub(r'\n\n+', '\n\n', text)

    return text.strip()

def test_text_cleaning():
    """Testa a limpeza do texto extraído"""
    print("=== Teste de Limpeza de Texto ===")

    # Extrair texto bruto
    raw_text = extract_text_from_pdf("_indextornos.pdf")
    if not raw_text:
        print("Erro: Não foi possível extrair texto do PDF")
        return

    print(f"Texto bruto: {len(raw_text)} caracteres")

    # Mostrar uma amostra do texto bruto
    print("\n--- TEXTO BRUTO (primeiros 500 chars) ---")
    print(repr(raw_text[:500]))

    # Limpar texto
    cleaned_text = clean_extracted_text(raw_text)
    print(f"\nTexto limpo: {len(cleaned_text)} caracteres")

    # Mostrar uma amostra do texto limpo
    print("\n--- TEXTO LIMPO (primeiros 500 chars) ---")
    print(repr(cleaned_text[:500]))

    # Comparar algumas estatísticas
    raw_lines = raw_text.count('\n')
    cleaned_lines = cleaned_text.count('\n')

    print(f"\n--- ESTATÍSTICAS ---")
    print(f"Quebras de linha - Bruto: {raw_lines}, Limpo: {cleaned_lines}")
    print(f"Redução de tamanho: {len(raw_text) - len(cleaned_text)} caracteres")

    # Procurar por padrões problemáticos no texto limpo
    print(f"\n--- VERIFICAÇÕES ---")

    # Verificar se ainda há palavras isoladas em linhas
    lines = cleaned_text.split('\n')
    single_word_lines = [line for line in lines if len(line.split()) == 1 and len(line) < 10]
    print(f"Linhas com uma palavra só: {len(single_word_lines)}")
    if single_word_lines[:5]:
        print(f"Exemplos: {single_word_lines[:5]}")

if __name__ == "__main__":
    test_text_cleaning()
