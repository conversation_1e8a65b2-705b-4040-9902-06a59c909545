import PyPDF2
from pdfminer.high_level import extract_text
import fitz  # PyMuPDF
import re

def extract_with_pypdf2(pdf_path: str) -> str:
    """Extrai texto usando PyPDF2"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                text += page_text + "\n"
        return text
    except Exception as e:
        print(f"Erro PyPDF2: {e}")
        return ""

def extract_with_pdfminer(pdf_path: str) -> str:
    """Extrai texto usando pdfminer.six"""
    try:
        text = extract_text(pdf_path)
        return text
    except Exception as e:
        print(f"Erro pdfminer: {e}")
        return ""

def extract_with_pymupdf(pdf_path: str) -> str:
    """Extrai texto usando PyMuPDF (fitz)"""
    try:
        doc = fitz.open(pdf_path)
        text = ""

        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            text += page_text + "\n"

        doc.close()
        return text
    except Exception as e:
        print(f"Erro PyMuPDF: {e}")
        return ""

def compare_extraction():
    """Compara a extração entre PyPDF2, pdfminer.six e PyMuPDF"""
    print("=== Comparação PyPDF2 vs pdfminer.six vs PyMuPDF ===")

    pdf_path = "_indextornos.pdf"

    # Extrair com PyPDF2
    print("Extraindo com PyPDF2...")
    pypdf2_text = extract_with_pypdf2(pdf_path)

    # Extrair com pdfminer
    print("Extraindo com pdfminer.six...")
    pdfminer_text = extract_with_pdfminer(pdf_path)

    # Extrair com PyMuPDF
    print("Extraindo com PyMuPDF...")
    pymupdf_text = extract_with_pymupdf(pdf_path)

    print(f"\n--- ESTATÍSTICAS ---")
    print(f"PyPDF2: {len(pypdf2_text)} caracteres")
    print(f"pdfminer: {len(pdfminer_text)} caracteres")
    print(f"PyMuPDF: {len(pymupdf_text)} caracteres")

    print(f"\n--- AMOSTRA PyPDF2 (primeiros 300 chars) ---")
    print(repr(pypdf2_text[:300]))

    print(f"\n--- AMOSTRA pdfminer (primeiros 300 chars) ---")
    print(repr(pdfminer_text[:300]))

    print(f"\n--- AMOSTRA PyMuPDF (primeiros 300 chars) ---")
    print(repr(pymupdf_text[:300]))

    # Procurar por palavras específicas que estavam cortadas
    test_words = ["portfolio", "2017", "máquinas", "INDEX", "encontro"]

    print(f"\n--- TESTE DE PALAVRAS ESPECÍFICAS ---")
    for word in test_words:
        pypdf2_count = pypdf2_text.lower().count(word.lower())
        pdfminer_count = pdfminer_text.lower().count(word.lower())
        pymupdf_count = pymupdf_text.lower().count(word.lower())
        print(f"'{word}': PyPDF2={pypdf2_count}, pdfminer={pdfminer_count}, PyMuPDF={pymupdf_count}")

if __name__ == "__main__":
    compare_extraction()
