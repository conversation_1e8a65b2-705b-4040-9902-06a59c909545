# PDF Embedding Script

Este script processa arquivos PDF, extrai o texto, gera embeddings usando OpenAI e armazena no Supabase vector store.

## Funcionalidades

- ✅ Extração de texto de arquivos PDF
- ✅ Divisão inteligente do texto em chunks
- ✅ Geração de embeddings usando OpenAI text-embedding-3-large
- ✅ Armazenamento no Supabase vector store
- ✅ Metadados detalhados para cada chunk

## Configuração

1. **Instalar dependências:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configurar variáveis de ambiente:**
   - Copie `.env.example` para `.env`
   - Preencha com suas credenciais:
     - `OPENAI_API_KEY`: Sua chave da API OpenAI
     - `SUPABASE_URL`: URL do seu projeto Supabase
     - `SUPABASE_KEY`: Chave anon do Supabase

3. **Banco de dados Supabase:**
   - Tabela: `documents_homologacao`
   - <PERSON><PERSON>a já configurado com suporte a vector embeddings

## Uso

1. Coloque seu arquivo PDF no diretório do projeto
2. Execute o script:
   ```bash
   python main.py
   ```

## Estrutura do Projeto

```
pdf-embedding2/
├── main.py              # Script principal
├── requirements.txt     # Dependências Python
├── .env                # Variáveis de ambiente (não commitado)
├── .env.example        # Exemplo de configuração
├── README.md           # Este arquivo
└── *.pdf              # Seus arquivos PDF
```

## Modelo de Embedding

Utiliza o modelo mais recente da OpenAI: **text-embedding-3-large**
- Dimensões: 3072
- Melhor qualidade para busca semântica
- Suporte a múltiplos idiomas
