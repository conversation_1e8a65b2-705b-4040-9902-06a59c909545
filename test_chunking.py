import PyPDF2
from typing import List

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai texto de um arquivo PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Erro ao extrair texto do PDF: {e}")
        return ""

def split_text_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Divide o texto em chunks menores para processamento, garantindo palavras completas"""
    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size

        # Se não chegamos ao final do texto, ajustar para não cortar palavras
        if end < len(text):
            chunk = text[start:end]

            # Procurar por quebras naturais (em ordem de preferência)
            # 1. Quebra de parágrafo
            last_paragraph = chunk.rfind('\n\n')
            # 2. Quebra de frase
            last_sentence = max(chunk.rfind('. '), chunk.rfind('.\n'), chunk.rfind('? '), chunk.rfind('! '))
            # 3. Quebra de linha
            last_newline = chunk.rfind('\n')
            # 4. Espaço (palavra completa)
            last_space = chunk.rfind(' ')

            # Escolher o melhor ponto de quebra
            break_point = -1

            # Preferir quebra de parágrafo se estiver na segunda metade do chunk
            if last_paragraph > chunk_size // 2:
                break_point = last_paragraph + 2  # +2 para incluir \n\n
            # Senão, preferir quebra de frase se estiver na segunda metade
            elif last_sentence > chunk_size // 2:
                break_point = last_sentence + 2  # +2 para incluir '. '
            # Senão, quebra de linha
            elif last_newline > chunk_size // 2:
                break_point = last_newline + 1  # +1 para incluir \n
            # Por último, quebra por palavra (espaço) - SEMPRE tentar isso
            elif last_space > 0:  # Qualquer espaço é melhor que cortar palavra
                break_point = last_space + 1  # +1 para incluir o espaço

            # Se encontrou um bom ponto de quebra, usar ele
            if break_point > 0:
                chunk = text[start:start + break_point]
                end = start + break_point
            else:
                # Se não encontrou nenhum espaço, usar o chunk completo (situação rara)
                chunk = text[start:end]
        else:
            # Último chunk - pegar todo o resto
            chunk = text[start:]

        # Adicionar chunk se não estiver vazio
        if chunk.strip():
            chunks.append(chunk.strip())

        # Calcular próximo início com overlap
        start = end - overlap

        # Garantir que não voltamos demais
        if start >= len(text):
            break

    return chunks

def test_chunking():
    """Testa o chunking e mostra os primeiros chunks"""
    print("=== Teste de Chunking ===")

    # Extrair texto do PDF
    text = extract_text_from_pdf("_indextornos.pdf")
    if not text:
        print("Erro: Não foi possível extrair texto do PDF")
        return

    print(f"Texto total: {len(text)} caracteres")

    # Dividir em chunks
    chunks = split_text_into_chunks(text, chunk_size=1000, overlap=200)
    print(f"Total de chunks: {len(chunks)}")

    # Mostrar os primeiros 3 chunks para verificar
    for i, chunk in enumerate(chunks[:3]):
        print(f"\n--- CHUNK {i+1} ({len(chunk)} chars) ---")
        print(f"Início: '{chunk[:50]}...'")
        print(f"Final: '...{chunk[-50:]}'")

        # Verificar se termina em palavra completa
        if chunk[-1].isalnum():
            print("⚠️  ATENÇÃO: Chunk termina no meio de uma palavra!")
        else:
            print("✓ Chunk termina corretamente")

    # Verificar overlap entre chunks
    if len(chunks) > 1:
        print(f"\n--- VERIFICAÇÃO DE OVERLAP ---")
        chunk1_end = chunks[0][-100:]  # Últimos 100 chars do chunk 1
        chunk2_start = chunks[1][:100]  # Primeiros 100 chars do chunk 2

        print(f"Final do chunk 1: '...{chunk1_end}'")
        print(f"Início do chunk 2: '{chunk2_start}...'")

if __name__ == "__main__":
    test_chunking()
