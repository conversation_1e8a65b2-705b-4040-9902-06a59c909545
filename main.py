import os
import sys
import uuid
import re
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client
import fitz  # PyMuPDF
from typing import List, Dict

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Verificar variáveis
if not all([OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY]):
    print("Erro: Verifique as variáveis de ambiente.")
    sys.exit(1)

# Inicializar clientes
openai_client = OpenAI(api_key=OPENAI_API_KEY)
supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# === FUNÇÕES ===

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai texto de um PDF com alta fidelidade usando PyMuPDF."""
    try:
        doc = fitz.open(pdf_path)
        full_text = ""

        for page in doc:
            page_dict = page.get_text("dict")
            for block in page_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        line_text = " ".join(span["text"] for span in line["spans"])
                        full_text += line_text.strip() + "\n"

        doc.close()
        return clean_extracted_text(full_text)

    except Exception as e:
        print(f"Erro ao extrair texto: {e}")
        return ""

def clean_extracted_text(text: str) -> str:
    """Limpa e normaliza o texto extraído do PDF."""
    text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', text)
    text = re.sub(r' +', ' ', text)
    text = re.sub(r'\n{3,}', '\n\n', text)
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    return "\n".join(lines)

def split_text_into_chunks(text: str, chunk_size_words: int = 250, overlap_words: int = 40) -> List[str]:
    """Divide o texto em chunks com sobreposição de palavras para evitar cortes e perda de contexto"""
    words = text.split()
    chunks = []
    start = 0

    while start < len(words):
        end = start + chunk_size_words
        chunk_words = words[start:end]

        # Verificação de quebra por frase (opcional)
        if end < len(words):
            # Evita cortar no meio de pontuação final
            while end < len(words) and not words[end - 1].endswith(('.', '!', '?')):
                chunk_words.append(words[end])
                end += 1

        chunk = ' '.join(chunk_words)
        chunks.append(chunk)

        # Avança para o próximo chunk com sobreposição
        start = end - overlap_words

        if start >= len(words):
            break

    return chunks


def generate_embedding(text: str) -> List[float]:
    """Gera embedding usando OpenAI."""
    try:
        response = openai_client.embeddings.create(
            model="text-embedding-3-small",
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Erro ao gerar embedding: {e}")
        return []

def save_to_supabase(content: str, metadata: Dict, embedding: List[float]) -> bool:
    """Salva o chunk no Supabase."""
    try:
        data = {
            "content": content,
            "metadata": metadata,
            "embedding": embedding
        }
        supabase_client.table("documents_homologacao").insert(data).execute()
        return True
    except Exception as e:
        print(f"Erro ao salvar no Supabase: {e}")
        return False

def process_pdf(pdf_path: str) -> None:
    """Executa o pipeline completo: extração, chunking, embedding, armazenamento."""
    print(f"📄 Processando PDF: {pdf_path}")

    text = extract_text_from_pdf(pdf_path)
    if not text:
        print("❌ Não foi possível extrair o texto.")
        return

    print(f"✅ Texto extraído com {len(text)} caracteres.")

    chunks = split_text_into_chunks(text)
    print(f"🧩 {len(chunks)} chunks criados.")

    success = 0

    for i, chunk in enumerate(chunks):
        print(f"➡️ Processando chunk {i+1}/{len(chunks)}...")
        embedding = generate_embedding(chunk)
        if not embedding:
            print("⚠️ Falha ao gerar embedding.")
            continue

        metadata = {
            "source_file": os.path.basename(pdf_path),
            "chunk_index": i,
            "total_chunks": len(chunks),
            "chunk_size": len(chunk),
            "processed_at": str(uuid.uuid4())
        }

        if save_to_supabase(chunk, metadata, embedding):
            print("💾 Chunk salvo com sucesso.")
            success += 1
        else:
            print("❌ Falha ao salvar chunk.")

    print(f"\n✅ Processamento finalizado: {success}/{len(chunks)} chunks salvos com sucesso.")

def main():
    print("=== PDF Embedding Script ===")
    pdfs = [f for f in os.listdir('.') if f.endswith('.pdf')]

    if not pdfs:
        print("Nenhum arquivo PDF encontrado.")
        return

    process_pdf(pdfs[0])

if __name__ == "__main__":
    main()