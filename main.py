﻿import os
import sys
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client
import PyPDF2
import json
from typing import List, Dict
import uuid

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Verificar se as variáveis estão configuradas
if not all([OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY]):
    print("Erro: Verifique se todas as variáveis de ambiente estão configuradas no arquivo .env")
    sys.exit(1)

# Inicializar clientes
openai_client = OpenAI(api_key=OPENAI_API_KEY)
supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai texto de um arquivo PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Erro ao extrair texto do PDF: {e}")
        return ""

def split_text_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Divide o texto em chunks menores para processamento"""
    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]

        # Tentar quebrar em uma frase completa
        if end < len(text):
            last_period = chunk.rfind('.')
            last_newline = chunk.rfind('\n')
            break_point = max(last_period, last_newline)

            if break_point > start + chunk_size // 2:
                chunk = text[start:start + break_point + 1]
                end = start + break_point + 1

        chunks.append(chunk.strip())
        start = end - overlap

        if start >= len(text):
            break

    return [chunk for chunk in chunks if chunk]

def generate_embedding(text: str) -> List[float]:
    """Gera embedding usando OpenAI"""
    try:
        response = openai_client.embeddings.create(
            model="text-embedding-3-large",  # Modelo mais recente
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Erro ao gerar embedding: {e}")
        return []

def save_to_supabase(content: str, metadata: Dict, embedding: List[float]) -> bool:
    """Salva o chunk e embedding no Supabase"""
    try:
        data = {
            "content": content,
            "metadata": metadata,
            "embedding": embedding
        }

        result = supabase_client.table("documents_homologacao").insert(data).execute()
        return True
    except Exception as e:
        print(f"Erro ao salvar no Supabase: {e}")
        return False

def process_pdf(pdf_path: str) -> None:
    """Processa um PDF completo: extração, chunking, embedding e armazenamento"""
    print(f"Processando PDF: {pdf_path}")

    # 1. Extrair texto
    print("1. Extraindo texto do PDF...")
    text = extract_text_from_pdf(pdf_path)
    if not text:
        print("Erro: Não foi possível extrair texto do PDF")
        return

    print(f"Texto extraído: {len(text)} caracteres")

    # 2. Dividir em chunks
    print("2. Dividindo texto em chunks...")
    chunks = split_text_into_chunks(text)
    print(f"Criados {len(chunks)} chunks")

    # 3. Processar cada chunk
    print("3. Gerando embeddings e salvando no Supabase...")
    successful_saves = 0

    for i, chunk in enumerate(chunks):
        print(f"Processando chunk {i+1}/{len(chunks)}...")

        # Gerar embedding
        embedding = generate_embedding(chunk)
        if not embedding:
            print(f"Falha ao gerar embedding para chunk {i+1}")
            continue

        # Preparar metadata
        metadata = {
            "source_file": os.path.basename(pdf_path),
            "chunk_index": i,
            "total_chunks": len(chunks),
            "chunk_size": len(chunk),
            "processed_at": str(uuid.uuid4())
        }

        # Salvar no Supabase
        if save_to_supabase(chunk, metadata, embedding):
            successful_saves += 1
            print(f"✓ Chunk {i+1} salvo com sucesso")
        else:
            print(f"✗ Falha ao salvar chunk {i+1}")

    print(f"\nProcessamento concluído!")
    print(f"Chunks processados com sucesso: {successful_saves}/{len(chunks)}")

def main():
    """Função principal"""
    print("=== PDF Embedding Script ===")
    print("Usando modelo de embedding: text-embedding-3-large")

    # Verificar se há um PDF no diretório
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]

    if not pdf_files:
        print("Nenhum arquivo PDF encontrado no diretório atual.")
        return

    print(f"PDFs encontrados: {pdf_files}")

    # Processar o primeiro PDF encontrado
    pdf_path = pdf_files[0]
    process_pdf(pdf_path)

if __name__ == "__main__":
    main()
from supabase import create_client, Client
import PyPDF2
import json
from typing import List, Dict
import uuid

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Verificar se as variáveis estão configuradas
if not all([OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY]):
    print("Erro: Verifique se todas as variáveis de ambiente estão configuradas no arquivo .env")
    sys.exit(1)

# Inicializar clientes
openai_client = OpenAI(api_key=OPENAI_API_KEY)
supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai texto de um arquivo PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Erro ao extrair texto do PDF: {e}")
        return ""

def split_text_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Divide o texto em chunks menores para processamento"""
    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]

        # Tentar quebrar em uma frase completa
        if end < len(text):
            last_period = chunk.rfind('.')
            last_newline = chunk.rfind('\n')
            break_point = max(last_period, last_newline)

            if break_point > start + chunk_size // 2:
                chunk = text[start:start + break_point + 1]
                end = start + break_point + 1

        chunks.append(chunk.strip())
        start = end - overlap

        if start >= len(text):
            break

    return [chunk for chunk in chunks if chunk]

def generate_embedding(text: str) -> List[float]:
    """Gera embedding usando OpenAI"""
    try:
        response = openai_client.embeddings.create(
            model="text-embedding-3-large",  # Modelo mais recente
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Erro ao gerar embedding: {e}")
        return []

def save_to_supabase(content: str, metadata: Dict, embedding: List[float]) -> bool:
    """Salva o chunk e embedding no Supabase"""
    try:
        data = {
            "content": content,
            "metadata": metadata,
            "embedding": embedding
        }

        result = supabase_client.table("documents_homologacao").insert(data).execute()
        return True
    except Exception as e:
        print(f"Erro ao salvar no Supabase: {e}")
        return False

def process_pdf(pdf_path: str) -> None:
    """Processa um PDF completo: extração, chunking, embedding e armazenamento"""
    print(f"Processando PDF: {pdf_path}")

    # 1. Extrair texto
    print("1. Extraindo texto do PDF...")
    text = extract_text_from_pdf(pdf_path)
    if not text:
        print("Erro: Não foi possível extrair texto do PDF")
        return

    print(f"Texto extraído: {len(text)} caracteres")

    # 2. Dividir em chunks
    print("2. Dividindo texto em chunks...")
    chunks = split_text_into_chunks(text)
    print(f"Criados {len(chunks)} chunks")

    # 3. Processar cada chunk
    print("3. Gerando embeddings e salvando no Supabase...")
    successful_saves = 0

    for i, chunk in enumerate(chunks):
        print(f"Processando chunk {i+1}/{len(chunks)}...")

        # Gerar embedding
        embedding = generate_embedding(chunk)
        if not embedding:
            print(f"Falha ao gerar embedding para chunk {i+1}")
            continue

        # Preparar metadata
        metadata = {
            "source_file": os.path.basename(pdf_path),
            "chunk_index": i,
            "total_chunks": len(chunks),
            "chunk_size": len(chunk),
            "processed_at": str(uuid.uuid4())
        }

        # Salvar no Supabase
        if save_to_supabase(chunk, metadata, embedding):
            successful_saves += 1
            print(f" Chunk {i+1} salvo com sucesso")
        else:
            print(f" Falha ao salvar chunk {i+1}")

    print(f"\nProcessamento concluído!")
    print(f"Chunks processados com sucesso: {successful_saves}/{len(chunks)}")

def main():
    """Função principal"""
    print("=== PDF Embedding Script ===")
    print("Usando modelo de embedding: text-embedding-3-large")

    # Verificar se há um PDF no diretório
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]

    if not pdf_files:
        print("Nenhum arquivo PDF encontrado no diretório atual.")
        return

    print(f"PDFs encontrados: {pdf_files}")

    # Processar o primeiro PDF encontrado
    pdf_path = pdf_files[0]
    process_pdf(pdf_path)

if __name__ == "__main__":
    main()
