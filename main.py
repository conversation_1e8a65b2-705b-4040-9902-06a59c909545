﻿import os
import sys
from dotenv import load_dotenv
from openai import OpenAI
from supabase import create_client, Client
import PyPDF2
import json
from typing import List, Dict
import uuid

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Verificar se as variáveis estão configuradas
if not all([OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY]):
    print("Erro: Verifique se todas as variáveis de ambiente estão configuradas no arquivo .env")
    sys.exit(1)

# Inicializar clientes
openai_client = OpenAI(api_key=OPENAI_API_KEY)
supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai e limpa texto de um arquivo PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                page_text = page.extract_text()
                text += page_text + "\n"

        # Limpar e normalizar o texto
        cleaned_text = clean_extracted_text(text)
        return cleaned_text
    except Exception as e:
        print(f"Erro ao extrair texto do PDF: {e}")
        return ""

def clean_extracted_text(text: str) -> str:
    """Limpa e normaliza o texto extraído do PDF"""
    import re

    # Remover caracteres de controle e normalizar espaços
    text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)

    # Corrigir quebras de linha desnecessárias (uma palavra por linha)
    # Múltiplas passadas para capturar todos os casos
    for _ in range(3):  # Fazer várias passadas
        # Padrão: palavra + \n + espaços + palavra
        text = re.sub(r'(\w+)\s*\n\s*(\w)', r'\1 \2', text)
        # Padrão: palavra + pontuação + \n + palavra
        text = re.sub(r'(\w+[,.;:])\s*\n\s*(\w)', r'\1 \2', text)
        # Padrão: palavra + \n + pontuação + \n + palavra
        text = re.sub(r'(\w+)\s*\n\s*([,.;:])\s*\n\s*(\w)', r'\1\2 \3', text)

    # Remover múltiplos espaços em branco
    text = re.sub(r' +', ' ', text)

    # Processar linha por linha para juntar linhas curtas
    lines = text.split('\n')
    processed_lines = []
    i = 0

    while i < len(lines):
        current_line = lines[i].strip()

        # Se a linha atual é muito curta (provavelmente palavra isolada)
        if current_line and len(current_line.split()) <= 2 and len(current_line) < 20:
            # Tentar juntar com a próxima linha
            if i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line:
                    # Juntar as linhas
                    combined = current_line + ' ' + next_line
                    processed_lines.append(combined)
                    i += 2  # Pular a próxima linha
                    continue

        processed_lines.append(current_line)
        i += 1

    # Reconstruir o texto
    text = '\n'.join(processed_lines)

    # Remover múltiplas quebras de linha, mantendo parágrafos
    text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)

    # Remover linhas vazias
    lines = [line for line in text.split('\n') if line.strip()]
    text = '\n'.join(lines)

    # Normalizar espaçamento entre parágrafos
    text = re.sub(r'\n\n+', '\n\n', text)

    return text.strip()

def split_text_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Divide o texto em chunks menores para processamento, garantindo palavras completas"""
    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size

        # Se não chegamos ao final do texto, ajustar para não cortar palavras
        if end < len(text):
            chunk = text[start:end]

            # Procurar por quebras naturais (em ordem de preferência)
            # 1. Quebra de parágrafo
            last_paragraph = chunk.rfind('\n\n')
            # 2. Quebra de frase
            last_sentence = max(chunk.rfind('. '), chunk.rfind('.\n'), chunk.rfind('? '), chunk.rfind('! '))
            # 3. Quebra de linha
            last_newline = chunk.rfind('\n')
            # 4. Espaço (palavra completa)
            last_space = chunk.rfind(' ')

            # Escolher o melhor ponto de quebra
            break_point = -1

            # Preferir quebra de parágrafo se estiver na segunda metade do chunk
            if last_paragraph > chunk_size // 2:
                break_point = last_paragraph + 2  # +2 para incluir \n\n
            # Senão, preferir quebra de frase se estiver na segunda metade
            elif last_sentence > chunk_size // 2:
                break_point = last_sentence + 2  # +2 para incluir '. '
            # Senão, quebra de linha
            elif last_newline > chunk_size // 2:
                break_point = last_newline + 1  # +1 para incluir \n
            # Por último, quebra por palavra (espaço) - SEMPRE tentar isso
            elif last_space > 0:  # Qualquer espaço é melhor que cortar palavra
                break_point = last_space + 1  # +1 para incluir o espaço

            # Se encontrou um bom ponto de quebra, usar ele
            if break_point > 0:
                chunk = text[start:start + break_point]
                end = start + break_point
            else:
                # Se não encontrou nenhum espaço, usar o chunk completo (situação rara)
                chunk = text[start:end]
        else:
            # Último chunk - pegar todo o resto
            chunk = text[start:]

        # Adicionar chunk se não estiver vazio
        if chunk.strip():
            chunks.append(chunk.strip())

        # Calcular próximo início com overlap
        start = end - overlap

        # Garantir que não voltamos demais
        if start >= len(text):
            break

    # Pós-processamento: verificar e corrigir palavras cortadas entre chunks
    chunks = fix_split_words_between_chunks(chunks)

    return chunks

def fix_split_words_between_chunks(chunks: List[str]) -> List[str]:
    """Corrige palavras que foram cortadas entre chunks"""
    if len(chunks) <= 1:
        return chunks

    fixed_chunks = []

    for i in range(len(chunks)):
        current_chunk = chunks[i].strip()

        # Verificar se o chunk atual começa com palavra incompleta
        if i > 0 and current_chunk:
            prev_chunk = fixed_chunks[-1]
            current_words = current_chunk.split()

            if current_words:
                first_word = current_words[0]
                prev_words = prev_chunk.split()

                # CASO 1: Números cortados (ex: "017" deveria ser "2017")
                if first_word.isdigit() and len(first_word) == 3 and prev_words:
                    prev_context = ' '.join(prev_words[-3:]).lower()
                    if any(word in prev_context for word in ['em', 'ano', 'desde', 'até']):
                        if first_word.startswith('0'):
                            current_words[0] = '2' + first_word
                            current_chunk = ' '.join(current_words)

                # CASO 2: Palavra começa com minúscula (muito provável ser continuação)
                elif (first_word.isalpha() and first_word[0].islower() and
                      len(first_word) >= 3 and prev_words):

                    last_word = prev_words[-1]

                    # Verificar se a última palavra não termina com pontuação final
                    if not last_word[-1] in '.!?':
                        # Juntar as palavras
                        combined_word = last_word + first_word

                        # Atualizar chunk anterior
                        prev_words[-1] = combined_word
                        fixed_chunks[-1] = ' '.join(prev_words)

                        # Remover primeira palavra do chunk atual
                        current_words = current_words[1:]
                        current_chunk = ' '.join(current_words)

                # CASO 3: Palavra muito curta que pode ser fragmento
                elif (first_word.isalpha() and len(first_word) <= 5 and
                      not first_word.lower() in ['a', 'o', 'e', 'em', 'de', 'da', 'do', 'na', 'no', 'se', 'que', 'com', 'por', 'para', 'uma', 'um', 'mas', 'seu', 'sua'] and
                      prev_words):

                    last_word = prev_words[-1]

                    # Se a última palavra é curta e não termina com pontuação
                    if (len(last_word) <= 6 and last_word.isalpha() and
                        not last_word[-1] in '.!?,:;)'):

                        # Juntar as palavras
                        combined_word = last_word + first_word

                        # Atualizar chunk anterior
                        prev_words[-1] = combined_word
                        fixed_chunks[-1] = ' '.join(prev_words)

                        # Remover primeira palavra do chunk atual
                        current_words = current_words[1:]
                        current_chunk = ' '.join(current_words)

        if current_chunk.strip():
            fixed_chunks.append(current_chunk)

    return fixed_chunks

def generate_embedding(text: str) -> List[float]:
    """Gera embedding usando OpenAI"""
    try:
        response = openai_client.embeddings.create(
            model="text-embedding-3-small",  # Modelo compatível com 1536 dimensões
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Erro ao gerar embedding: {e}")
        return []

def save_to_supabase(content: str, metadata: Dict, embedding: List[float]) -> bool:
    """Salva o chunk e embedding no Supabase"""
    try:
        data = {
            "content": content,
            "metadata": metadata,
            "embedding": embedding
        }

        result = supabase_client.table("documents_homologacao").insert(data).execute()
        return True
    except Exception as e:
        print(f"Erro ao salvar no Supabase: {e}")
        return False

def process_pdf(pdf_path: str) -> None:
    """Processa um PDF completo: extração, chunking, embedding e armazenamento"""
    print(f"Processando PDF: {pdf_path}")

    # 1. Extrair texto
    print("1. Extraindo texto do PDF...")
    text = extract_text_from_pdf(pdf_path)
    if not text:
        print("Erro: Não foi possível extrair texto do PDF")
        return

    print(f"Texto extraído: {len(text)} caracteres")

    # 2. Dividir em chunks
    print("2. Dividindo texto em chunks...")
    chunks = split_text_into_chunks(text)
    print(f"Criados {len(chunks)} chunks")

    # 3. Processar cada chunk
    print("3. Gerando embeddings e salvando no Supabase...")
    successful_saves = 0

    for i, chunk in enumerate(chunks):
        print(f"Processando chunk {i+1}/{len(chunks)}...")

        # Gerar embedding
        embedding = generate_embedding(chunk)
        if not embedding:
            print(f"Falha ao gerar embedding para chunk {i+1}")
            continue

        # Preparar metadata
        metadata = {
            "source_file": os.path.basename(pdf_path),
            "chunk_index": i,
            "total_chunks": len(chunks),
            "chunk_size": len(chunk),
            "processed_at": str(uuid.uuid4())
        }

        # Salvar no Supabase
        if save_to_supabase(chunk, metadata, embedding):
            successful_saves += 1
            print(f"✓ Chunk {i+1} salvo com sucesso")
        else:
            print(f"✗ Falha ao salvar chunk {i+1}")

    print(f"\nProcessamento concluído!")
    print(f"Chunks processados com sucesso: {successful_saves}/{len(chunks)}")

def main():
    """Função principal"""
    print("=== PDF Embedding Script ===")
    print("Usando modelo de embedding: text-embedding-3-small")

    # Verificar se há um PDF no diretório
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]

    if not pdf_files:
        print("Nenhum arquivo PDF encontrado no diretório atual.")
        return

    print(f"PDFs encontrados: {pdf_files}")

    # Processar o primeiro PDF encontrado
    pdf_path = pdf_files[0]
    process_pdf(pdf_path)

if __name__ == "__main__":
    main()
from supabase import create_client, Client
import PyPDF2
import json
from typing import List, Dict
import uuid

# Carregar variáveis de ambiente
load_dotenv()

# Configurações
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Verificar se as variáveis estão configuradas
if not all([OPENAI_API_KEY, SUPABASE_URL, SUPABASE_KEY]):
    print("Erro: Verifique se todas as variáveis de ambiente estão configuradas no arquivo .env")
    sys.exit(1)

# Inicializar clientes
openai_client = OpenAI(api_key=OPENAI_API_KEY)
supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extrai texto de um arquivo PDF"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Erro ao extrair texto do PDF: {e}")
        return ""

def split_text_into_chunks(text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
    """Divide o texto em chunks menores para processamento"""
    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]

        # Tentar quebrar em uma frase completa
        if end < len(text):
            last_period = chunk.rfind('.')
            last_newline = chunk.rfind('\n')
            break_point = max(last_period, last_newline)

            if break_point > start + chunk_size // 2:
                chunk = text[start:start + break_point + 1]
                end = start + break_point + 1

        chunks.append(chunk.strip())
        start = end - overlap

        if start >= len(text):
            break

    return [chunk for chunk in chunks if chunk]

def generate_embedding(text: str) -> List[float]:
    """Gera embedding usando OpenAI"""
    try:
        response = openai_client.embeddings.create(
            model="text-embedding-3-large",  # Modelo mais recente
            input=text
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Erro ao gerar embedding: {e}")
        return []

def save_to_supabase(content: str, metadata: Dict, embedding: List[float]) -> bool:
    """Salva o chunk e embedding no Supabase"""
    try:
        data = {
            "content": content,
            "metadata": metadata,
            "embedding": embedding
        }

        result = supabase_client.table("documents_homologacao").insert(data).execute()
        return True
    except Exception as e:
        print(f"Erro ao salvar no Supabase: {e}")
        return False

def process_pdf(pdf_path: str) -> None:
    """Processa um PDF completo: extração, chunking, embedding e armazenamento"""
    print(f"Processando PDF: {pdf_path}")

    # 1. Extrair texto
    print("1. Extraindo texto do PDF...")
    text = extract_text_from_pdf(pdf_path)
    if not text:
        print("Erro: Não foi possível extrair texto do PDF")
        return

    print(f"Texto extraído: {len(text)} caracteres")

    # 2. Dividir em chunks
    print("2. Dividindo texto em chunks...")
    chunks = split_text_into_chunks(text)
    print(f"Criados {len(chunks)} chunks")

    # 3. Processar cada chunk
    print("3. Gerando embeddings e salvando no Supabase...")
    successful_saves = 0

    for i, chunk in enumerate(chunks):
        print(f"Processando chunk {i+1}/{len(chunks)}...")

        # Gerar embedding
        embedding = generate_embedding(chunk)
        if not embedding:
            print(f"Falha ao gerar embedding para chunk {i+1}")
            continue

        # Preparar metadata
        metadata = {
            "source_file": os.path.basename(pdf_path),
            "chunk_index": i,
            "total_chunks": len(chunks),
            "chunk_size": len(chunk),
            "processed_at": str(uuid.uuid4())
        }

        # Salvar no Supabase
        if save_to_supabase(chunk, metadata, embedding):
            successful_saves += 1
            print(f" Chunk {i+1} salvo com sucesso")
        else:
            print(f" Falha ao salvar chunk {i+1}")

    print(f"\nProcessamento concluído!")
    print(f"Chunks processados com sucesso: {successful_saves}/{len(chunks)}")

def main():
    """Função principal"""
    print("=== PDF Embedding Script ===")
    print("Usando modelo de embedding: text-embedding-3-large")

    # Verificar se há um PDF no diretório
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]

    if not pdf_files:
        print("Nenhum arquivo PDF encontrado no diretório atual.")
        return

    print(f"PDFs encontrados: {pdf_files}")

    # Processar o primeiro PDF encontrado
    pdf_path = pdf_files[0]
    process_pdf(pdf_path)

if __name__ == "__main__":
    main()
