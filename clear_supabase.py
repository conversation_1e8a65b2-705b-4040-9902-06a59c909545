import os
from dotenv import load_dotenv
from supabase import create_client, Client

# Carregar variáveis de ambiente
load_dotenv()

SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')

# Inicializar cliente
supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

def clear_documents():
    """Limpa todos os documentos da tabela documents_homologacao"""
    try:
        # Deletar todos os registros
        result = supabase_client.table("documents_homologacao").delete().neq('id', '00000000-0000-0000-0000-000000000000').execute()
        print(f"✓ Tabela limpa com sucesso!")
        return True
    except Exception as e:
        print(f"Erro ao limpar tabela: {e}")
        return False

if __name__ == "__main__":
    print("=== Limpando dados antigos do Supabase ===")
    clear_documents()
